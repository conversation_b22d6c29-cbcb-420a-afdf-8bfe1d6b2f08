import userModel from "../models/userModel";

export const getUsers = async (req, res) => {
    try{
        const users = await userModel.find({}).select("-password");
        res.status(200).json({
            msg: "Users fetched successfully",
            users,
        });
    }
    catch(err){
        console.error("Get users error:", err);
        res.status(500).json({ msg: "Server error during get users" });
    }
}
