import React, { useState, useEffect, useContext } from "react";
import { AuthContext } from "../contexts/AuthContext";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import { adminAPI } from "../utils/api";

function Admin() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [users, setUsers] = useState([]);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    if (user && user.role === "admin") {
      setIsLoggedIn(true);
      fetchUsers();
    }
  }, [user]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await adminAPI.login({ email, password });
      localStorage.setItem('token', response.data.token);
      setIsLoggedIn(true);
      fetchUsers();
    } catch (error) {
      setError(error.response?.data?.msg || "Admin login failed");
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await adminAPI.getUsers();
      setUsers(response.data.users);
    } catch (err) {
      console.error("Error fetching users:", err);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      try {
        await adminAPI.deleteUser(userId);
        fetchUsers(); // Refresh list
      } catch (err) {
        console.error("Error deleting user:", err);
      }
    }
  };

  if (!isLoggedIn) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
          <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-full max-w-md">
            <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">Admin Login</h2>
            
            {error && (
              <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-400 text-red-700 dark:text-red-400 rounded">
                {error}
              </div>
            )}

            <form onSubmit={handleLogin}>
              <input
                value={email}
                type="email"
                placeholder="Admin Email"
                className="w-full mb-4 p-2 border dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
                required
              />
              <input
                value={password}
                type="password"
                placeholder="Admin Password"
                className="w-full mb-4 p-2 border dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                required
              />
              <button
                className="w-full bg-blue-600 text-white p-2 rounded hover:bg-blue-700 disabled:bg-blue-400"
                type="submit"
                disabled={loading}
              >
                {loading ? "Logging in..." : "Admin Login"}
              </button>
            </form>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-6">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">Admin Dashboard</h1>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">User Management</h2>
            
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left p-2 text-gray-900 dark:text-white">Username</th>
                    <th className="text-left p-2 text-gray-900 dark:text-white">Email</th>
                    <th className="text-left p-2 text-gray-900 dark:text-white">Role</th>
                    <th className="text-left p-2 text-gray-900 dark:text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user._id} className="border-b dark:border-gray-700">
                      <td className="p-2 text-gray-900 dark:text-white">{user.username}</td>
                      <td className="p-2 text-gray-900 dark:text-white">{user.email}</td>
                      <td className="p-2 text-gray-900 dark:text-white">{user.role}</td>
                      <td className="p-2">
                        {user.role !== "admin" && (
                          <button
                            onClick={() => handleDeleteUser(user._id)}
                            className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
                          >
                            Delete
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default Admin;
